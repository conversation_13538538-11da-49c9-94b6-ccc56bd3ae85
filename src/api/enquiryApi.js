import apiClient from './apiClient';
import logger from '../utils/logger';

/**
 * Enquiry API methods
 */
const enquiryApi = {
  /**
   * Submit a new enquiry
   * @param {Object} enquiryData - Enquiry data
   * @param {string} enquiryData.type - Paper type
   * @param {string} enquiryData.gsm - GSM value
   * @param {string} enquiryData.bf - BF value
   * @param {string} enquiryData.width - Width value
   * @param {number} enquiryData.quantity - Quantity
   * @param {string} enquiryData.message - Optional message
   * @returns {Promise} - Promise with enquiry data
   */
  submitEnquiry: async (enquiryData) => {
    return logger.trackApiCall('enquiry', 'submitEnquiry', async () => {
      // With our new fetch-based client, the response is already the data
      return await apiClient.post('/enquiries', enquiryData);
    });
  },

  /**
   * Get user's enquiry history
   * @param {Object} params - Query parameters
   * @param {number} params.page - Page number for pagination
   * @param {number} params.limit - Number of items per page
   * @param {string} params.status - Filter by enquiry status
   * @returns {Promise} - Promise with enquiry history data
   */
  getEnquiries: async (params = {}) => {
    return logger.trackApiCall('enquiry', 'getEnquiries', async () => {
      // With our new fetch-based client, the response is already the data
      return await apiClient.get('/enquiries', { params });
    });
  },

  /**
   * Get enquiry details by ID
   * @param {string} enquiryId - ID of the enquiry to retrieve
   * @returns {Promise} - Promise with enquiry details
   */
  getEnquiryDetails: async (enquiryId) => {
    return logger.trackApiCall('enquiry', 'getEnquiryDetails', async () => {
      // With our new fetch-based client, the response is already the data
      return await apiClient.get(`/enquiries/${enquiryId}`);
    });
  },

  /**
   * Get all enquiries (Admin only)
   * @param {Object} params - Query parameters
   * @returns {Promise} - Promise with all enquiries data
   */
  getAllEnquiries: async (params = {}) => {
    return logger.trackApiCall('enquiry', 'getAllEnquiries', async () => {
      // With our new fetch-based client, the response is already the data
      return await apiClient.get('/admin/enquiries', { params });
    });
  },

  /**
   * Respond to an enquiry (Admin only)
   * @param {string} enquiryId - ID of the enquiry to respond to
   * @param {string} responseMessage - Response message
   * @returns {Promise} - Promise with updated enquiry data
   */
  respondToEnquiry: async (enquiryId, responseMessage) => {
    return logger.trackApiCall('enquiry', 'respondToEnquiry', async () => {
      // With our new fetch-based client, the response is already the data
      return await apiClient.post(`/admin/enquiries/${enquiryId}/respond`, { response: responseMessage });
    });
  },
};

export default enquiryApi;
