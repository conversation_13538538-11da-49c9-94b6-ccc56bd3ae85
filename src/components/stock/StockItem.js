import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { colors } from '../../theme/colors';
import { spacing } from '../../theme/spacing';
import { textStyles } from '../../theme/typography';

const StockItem = ({ item, onPress, paymentTerms = 'IMMEDIATE' }) => {
  // Function to get price based on user's payment terms
  

  return (
    <TouchableOpacity style={styles.container} onPress={() => onPress(item)} activeOpacity={0.7}>
      <View style={styles.row}>
        <View style={styles.column}>
          <Text style={styles.label}>Type</Text>
          <Text style={styles.value}>{item.type}</Text>
        </View>
        
        <View style={styles.column}>
          <Text style={styles.label}>GSM</Text>
          <Text style={styles.value}>{item.gsm}</Text>
        </View>
        
        <View style={styles.column}>
          <Text style={styles.label}>BF</Text>
          <Text style={styles.value}>{item.bf}</Text>
        </View>

        <View style={styles.column}>
          <Text style={styles.label}>Width(mm)</Text>
          <Text style={styles.priceValue}>{item.width}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 0,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    paddingVertical: spacing.small,
    marginHorizontal: 0,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.medium,
  },
  column: {
    alignItems: 'center',
    flex: 1,
  },
  label: {
    ...textStyles.body2,
    color: colors.textLight,
    marginBottom: spacing.tiny,
  },
  value: {
    ...textStyles.body1,
    color: colors.textDark,
    fontWeight: '500',
  },
  priceValue: {
    ...textStyles.body1,
    color: colors.primary,
    fontWeight: '600',
  },
});

export default StockItem;
