import React, { createContext, useState, useEffect, useContext } from 'react';
import { authApi } from '../../api';
import AsyncStorage from '@react-native-async-storage/async-storage';
import statePersistence from '../../utils/statePersistence';
import logger from '../../utils/simpleLogger';
import alertManager from '../../utils/alertManager';
import { getAuth, signInWithCustomToken } from '@react-native-firebase/auth';
import { verifyPhoneNumber as firebaseVerifyPhoneNumber } from '../../utils/firebase';

// Storage keys for persisted state
const STORAGE_KEYS = {
  USER: 'app:auth:user',
  IS_AUTHENTICATED: 'app:auth:isAuthenticated',
  VERIFICATION_DATA: 'app:auth:verificationData',
  REGISTRATION_DATA: 'app:auth:registrationData',
};

// Create the auth context
const AuthContext = createContext();

/**
 * Auth Provider component to wrap the app and provide authentication state
 */
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [registrationData, setRegistrationData] = useState(null);
  const [verificationData, setVerificationData] = useState(null);

  // Flag to prevent multiple simultaneous auth checks
  const [isCheckingAuth, setIsCheckingAuth] = useState(false);

  // Custom state setters that also persist to AsyncStorage
  const persistUser = async (userData) => {
    setUser(userData);
    await statePersistence.saveState(STORAGE_KEYS.USER, userData);
  };

  const persistIsAuthenticated = async (value) => {
    // Only update if the value actually changed to prevent unnecessary re-renders
    setIsAuthenticated(prevValue => {
      if (prevValue !== value) {
        statePersistence.saveState(STORAGE_KEYS.IS_AUTHENTICATED, value)
          .catch(err => console.error('Failed to persist auth state:', err));
        return value;
      }
      return prevValue;
    });
  };

  const persistRegistrationData = async (data) => {
    setRegistrationData(data);
    await statePersistence.saveState(STORAGE_KEYS.REGISTRATION_DATA, data);
  };

  const persistVerificationData = async (data) => {
    setVerificationData(data);
    statePersistence.saveState(STORAGE_KEYS.VERIFICATION_DATA, data)
      .catch(err => console.error('Failed to persist verification data:', err));
  };

  // Load persisted state on mount
  useEffect(() => {
    const loadPersistedState = async () => {
      try {
        logger.debug('Loading persisted authentication state...');

        const [
          persistedUser,
          persistedIsAuthenticated,
          persistedRegistrationData,
          persistedVerificationData
        ] = await Promise.all([
          statePersistence.loadState(STORAGE_KEYS.USER, null),
          statePersistence.loadState(STORAGE_KEYS.IS_AUTHENTICATED, false),
          statePersistence.loadState(STORAGE_KEYS.REGISTRATION_DATA, null),
          statePersistence.loadState(STORAGE_KEYS.VERIFICATION_DATA, null)
        ]);

        logger.debug('Persisted authentication state loaded');

        if (persistedUser) setUser(persistedUser);
        if (persistedIsAuthenticated !== null) setIsAuthenticated(persistedIsAuthenticated);
        if (persistedRegistrationData) setRegistrationData(persistedRegistrationData);
        if (persistedVerificationData) setVerificationData(persistedVerificationData);

        await checkAuthStatus();
      } catch (error) {
        console.error('Failed to load persisted state:', error);
        setIsLoading(false);
      }
    };

    loadPersistedState();
  }, []);

  // Check authentication status
  const checkAuthStatus = async () => {
    // Prevent multiple simultaneous auth checks
    if (isCheckingAuth) {
      return;
    }

    setIsCheckingAuth(true);
    
    try {
      logger.debug('Checking authentication status...');

      const token = await AsyncStorage.getItem('auth_token');
      console.log('Auth check - token exists:', !!token);

      if (token) {
        try {
          const userData = await authApi.getCurrentUser();
          
          // Only update state if user data actually changed
          console.log("userData in auth-contex is : " , userData.user)

          if (JSON.stringify(userData) !== JSON.stringify(user)) {
            // Extract and structure user data properly
            const processedUserData = {
              ...userData.user , 
              idToken: token,
              customToken: userData.customToken
              
            };
            await persistUser(processedUserData);
          }
          
          // Only update auth state if it's not already true
          if (!isAuthenticated) {
            await persistIsAuthenticated(true);
          }
          
          logger.debug('User authenticated successfully');
        } catch (userError) {
          console.warn('Failed to get user data, but not clearing auth immediately:', userError);
          
          // Only clear auth if it's a 401 error (invalid token)
          if (userError.status === 401) {
            console.log('Token is invalid, clearing auth state');
            await clearAuthState();
          } else {
            // For other errors (network, server), keep the user logged in
            logger.debug('Keeping user authenticated due to non-auth error');
          }
        }
      } else {
        // Only clear state if we currently think we're authenticated
        if (isAuthenticated || user) {
          console.log('No token found, clearing auth state');
          await clearAuthState();
        }
        logger.debug('No authentication token found');
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      // Don't clear auth state on network errors
    } finally {
      setIsLoading(false);
      setIsCheckingAuth(false);
    }
  };

  // Helper function to clear auth state
  const clearAuthState = async () => {
    await persistUser(null);
    await persistIsAuthenticated(false);
    // Don't clear registration/verification data as they might be needed
  };

  /**
   * Register a new user
   */
  const register = async (userData) => {
    setIsLoading(true);
    try {
      await persistRegistrationData(userData);

      persistVerificationData({
        type: 'email',
        email: userData.email,
        password: userData.password,
        registrationInProgress: true,
      });

      console.log('Registration data stored, proceeding to email verification');

      return {
        status: 'success',
        message: 'Registration data stored, proceeding to verification',
        data: {
          email: userData.email
        }
      };
    } catch (error) {
      let errorMessage = error.response?.data?.message || error.message || 'An error occurred';

      if (error.response?.data?.errors && Array.isArray(error.response.data.errors)) {
        const validationErrors = error.response.data.errors;
        errorMessage = validationErrors.map(err => `${err.field}: ${err.message}`).join('\n');
      }

      alertManager.showError('Registration failed', errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Login with email and password
   */
  /**
 * Login with email and password
 */
const login = async (email, password) => {
  setIsLoading(true);
  try {
    const response = await authApi.login(email, password);

    if (response.status === 'success' && response.data && response.data.rider) {
      const { customToken } = response.data.rider;
      
      // If we have a custom token, sign in with Firebase
      if (customToken) {
        try {
          const auth = getAuth();
          // Exchange the custom token for an ID token
          const userCredential = await signInWithCustomToken(auth, customToken);
          
          // Get the Firebase ID token
          const idToken = await userCredential.user.getIdToken();
          
          // Store the ID token as 'auth_token' for API requests
          await AsyncStorage.setItem('auth_token', idToken);
          
          // Save user data and set authenticated
          await persistUser({
            ...response.data.rider,
            user: response.data.rider, // Make sure user data is accessible via user property
            idToken
          });
          await persistIsAuthenticated(true);
          
          return response;
        } catch (firebaseError) {
          logger.error('Firebase authentication failed:', firebaseError);
          alertManager.showError(
            'Login Error',
            'Firebase authentication failed. Please try again.'
          );
          throw firebaseError;
        }
      } else {
        // Handle the case without a custom token
        logger.warn('Login succeeded but custom token is missing');
        await persistUser(response.data.rider);
        await persistIsAuthenticated(true);
        return response;
      }
    } else {
      // Handle invalid response
      throw new Error('Invalid response format');
    }
  } catch (error) {
    throw error;
  } finally {
    setIsLoading(false);
  }
};

  /**
   * Request OTP for phone verification
   */
  const requestPhoneOtp = async (phoneNumber) => {
    setIsLoading(true);
    try {
      const response = await authApi.requestPhoneOtp(phoneNumber);

      if (response.status === 'success' && response.data) {
        persistVerificationData({
          type: 'phone',
          phoneNumber,
          verificationId: response.data.verificationId,
        });

        alertManager.showSuccess(
          'OTP Sent',
          'Please check your phone for the verification code.',
          [{
            text: 'OK',
            onPress: () => {
              console.log('User acknowledged OTP sent');
            }
          }]
        );
      } else {
        console.warn('Unexpected phone OTP response format:', response);
        if (response.verificationId) {
          persistVerificationData({
            type: 'phone',
            phoneNumber,
            verificationId: response.verificationId,
          });
        }
      }

      return response;
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || 'An error occurred';
      alertManager.showError('Failed to request OTP', errorMessage, [{
        text: 'OK',
        onPress: () => {
          console.log('User acknowledged phone OTP request failure');
        }
      }]);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Verify phone OTP
   */
  const verifyPhoneOtp = async (otp) => {
    if (!verificationData || verificationData.type !== 'phone') {
      throw new Error('No phone verification in progress');
    }

    if (!verificationData.verificationId) {
      throw new Error('Missing verification ID. Please request a new OTP.');
    }

    setIsLoading(true);
    try {
      const response = await authApi.verifyPhoneOtp(
        verificationData.phoneNumber,
        verificationData.verificationId,
        otp
      );

      const isRegistration = verificationData.registrationInProgress;

      if (isRegistration && verificationData.emailVerified) {
        if (!registrationData) {
          throw new Error('Registration data is missing. Please start the registration process again.');
        }

        const firebaseUid = response.data?.firebaseUid;
        if (!firebaseUid) {
          throw new Error('Firebase UID is missing from verification response.');
        }

        const completeRegistrationData = {
          ...registrationData,
          firebaseUid
        };

        const registerResponse = await authApi.register(completeRegistrationData);

        if (registerResponse.status === 'success') {
          if (verificationData.email && verificationData.password) {
            try {
              const loginResponse = await authApi.login(
                verificationData.email,
                verificationData.password
              );

              if (loginResponse.data && loginResponse.data.user) {
                await persistUser(loginResponse.data.user);
                await persistIsAuthenticated(true);
              }
            } catch (loginError) {
              console.warn('Auto-login after registration failed:', loginError);
            }
          }

          persistVerificationData(null);
          return {
            ...registerResponse,
            message: 'Registration successful. Your account is pending approval.'
          };
        } else {
          throw new Error(registerResponse.message || 'Registration failed');
        }
      } else {
        if (response.data && response.data.user) {
          await persistUser(response.data.user);
          await persistIsAuthenticated(true);
        } else {
          console.warn('User data not found in verification response');
        }

        persistVerificationData(null);
        return response;
      }
    } catch (error) {
      let errorMessage = error.response?.data?.message || error.message || 'An error occurred';

      if (error.response?.data?.errors && Array.isArray(error.response.data.errors)) {
        const validationErrors = error.response.data.errors;
        errorMessage = validationErrors.map(err => `${err.field}: ${err.message}`).join('\n');
      }

      alertManager.showError('Verification failed', errorMessage, [{
        text: 'OK',
        onPress: () => {
          console.log('User acknowledged phone verification failure');
        }
      }]);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Request OTP for email verification
   */
  const requestEmailOtp = async (email, password = null, showAlert = true, onSuccess = null) => {
    setIsLoading(true);

    try {
      const response = await authApi.requestEmailOtp(email);

      const verificationInfo = {
        type: 'email',
        email,
        registrationInProgress: true,
      };

      if (password) {
        verificationInfo.password = password;
      }

      console.log('Setting verification data during registration:', verificationInfo);
      persistVerificationData(verificationInfo);

      if (response.status === 'success' && showAlert && !onSuccess) {
        alertManager.showSuccess('OTP Sent', 'Please check your email for the verification code.', [{
          text: 'OK',
          onPress: () => {
            console.log('User acknowledged email OTP sent');
          }
        }]);
      }

      if (onSuccess && typeof onSuccess === 'function') {
        onSuccess(response);
      }

      return response;
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || 'An error occurred';

      if (showAlert) {
        alertManager.showError('Failed to request OTP', errorMessage, [{
          text: 'OK',
          onPress: () => {
            console.log('User acknowledged email OTP request failure');
          }
        }]);
      }

      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Verify email OTP
   */
  const verifyEmailOtp = async (otp) => {
    if (!verificationData || verificationData.type !== 'email') {
      throw new Error('No email verification in progress');
    }

    setIsLoading(true);
    try {
      const verifyResponse = await authApi.verifyEmailOtp(
        verificationData.email,
        otp
      );

      const isRegistration = verificationData.registrationInProgress;

      if (verifyResponse.status === 'success') {
        if (isRegistration) {
          persistVerificationData({
            type: 'phone',
            email: verificationData.email,
            password: verificationData.password,
            registrationInProgress: true,
            emailVerified: true
          });

          return {
            status: 'success',
            message: 'Email verified successfully. Please verify your phone number.',
            data: {
              emailVerified: true,
              nextStep: 'phone_verification'
            }
          };
        } else if (verificationData.password) {
          const loginResponse = await authApi.login(
            verificationData.email,
            verificationData.password
          );

          if (loginResponse.data && loginResponse.data.user) {
            await persistUser(loginResponse.data.user);
            await persistIsAuthenticated(true);
          }

          persistVerificationData(null);
          return loginResponse;
        } else {
          persistVerificationData(null);
          return verifyResponse;
        }
      } else {
        alertManager.showError('Verification failed', verifyResponse.message || 'OTP verification failed', [{
          text: 'OK',
          onPress: () => {
            console.log('User acknowledged email verification failure');
          }
        }]);
        throw new Error(verifyResponse.message || 'OTP verification failed');
      }
    } catch (error) {
      let errorMessage = error.response?.data?.message || error.message || 'An error occurred';

      if (error.response?.data?.errors && Array.isArray(error.response.data.errors)) {
        const validationErrors = error.response.data.errors;
        errorMessage = validationErrors.map(err => `${err.field}: ${err.message}`).join('\n');
      }

      alertManager.showError('Verification failed', errorMessage, [{
        text: 'OK',
        onPress: () => {
          console.log('User acknowledged email verification failure');
        }
      }]);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Logout the current user
   */
const logout = async () => {
  setIsLoading(true);
  try {
    // Clear Firebase auth if using it
    try {
      const auth = getAuth();
      if (auth && auth.currentUser) {
        await auth.signOut();
      }
    } catch (firebaseError) {
      // Just log the error but continue with logout
      console.warn('Firebase sign out failed:', firebaseError.message);
    }
    
    // Clear stored tokens
    await AsyncStorage.removeItem('firebase_token');
    await AsyncStorage.removeItem('auth_token');
    
    // Clear auth state in this order (important for navigation flow)
    await persistIsAuthenticated(false);
    await persistUser(null);
    
    // Don't clear registration data
    console.log('User logged out successfully');
    return true;
  } catch (error) {
    console.error('Logout error:', error);
    return false;
  } finally {
    setIsLoading(false);
  }
};

  /**
   * Login with phone OTP
   */
  const loginWithPhoneOtp = async (phoneNumber, verificationId, code) => {
    setIsLoading(true);
    try {
      // First verify the phone number with Firebase to get the UID
      const verifyResponse = await firebaseVerifyPhoneNumber(verificationId, code);

      if (!verifyResponse.success) {
        throw new Error(verifyResponse.error || 'Phone verification failed');
      }

      const firebaseUid = verifyResponse.firebaseUid;

      if (!firebaseUid) {
        throw new Error('Firebase UID not found in verification response');
      }

      // Now call the backend with the Firebase UID
      const loginResponse = await authApi.loginWithPhone(firebaseUid);

      if (loginResponse.status === 'success' && loginResponse.data && loginResponse.data.rider) {
        await persistUser(loginResponse.data.rider);
        await persistIsAuthenticated(true);
      } else {
        console.warn('User data not found in login response');
      }

      return loginResponse;
    } catch (error) {
      let errorMessage = error.response?.data?.message || error.message || 'An error occurred';

      if (error.response?.data?.errors && Array.isArray(error.response.data.errors)) {
        const validationErrors = error.response.data.errors;
        errorMessage = validationErrors.map(err => `${err.field}: ${err.message}`).join('\n');
      }

      alertManager.showError('Login failed', errorMessage, [{
        text: 'OK',
        onPress: () => {
          console.log('User acknowledged phone login failure');
        }
      }]);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Context value
  const value = {
    user,
    isLoading,
    isAuthenticated,
    registrationData,
    verificationData,
    register,
    login,
    loginWithPhoneOtp,
    requestPhoneOtp,
    verifyPhoneOtp,
    requestEmailOtp,
    verifyEmailOtp,
    logout,
    setRegistrationData: persistRegistrationData,
    checkAuthStatus, // Expose this for manual auth checks if needed
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

/**
 * Custom hook to use the auth context
 */
export const useAuth = () => {
  const context = useContext(AuthContext);

  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }

  return context;
};

export default AuthContext;